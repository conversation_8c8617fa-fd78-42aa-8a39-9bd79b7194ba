Feature: File Upload

Background:
  Given The main page is loaded

@e2e @file-upload
Scenario: Verify user can upload a file successfully
  When The user clicks upload files button
  And The user clicks on new event button
  Then The user enter "test-upload-event" in the event name textbox
  And The user clicks on create event button
  Then The user should see a success snackbar with message "test-upload-event"
  And The user selects a file to upload "lucy.mp4"
  Then The file should be uploaded successfully
  And The file "lucy.mp4" should appear in the upload area
  Then The user click upload button
  And The upload should complete "100%"
  Then The user clicks on cancel upload button
  When The user selects the event named "test-upload-event"
  Then The following event details should be visible:
      | Field      | Expected Value |
      | File Count | 1 File         |
